package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.config.FileUpdateScheduleProperties;
import com.anytech.anytxn.authorization.base.domain.dto.*;
import com.anytech.anytxn.authorization.mapper.fileupdate.FileUpdateMapper;
import com.anytech.anytxn.authorization.base.domain.model.AuthorizationFileUpdate;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * @description AuthFileUpdateServiceImpl的单元测试类 - 简化版本
 * <AUTHOR>
 * @date 2025/01/08
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("授权文件更新服务测试")
class AuthFileUpdateServiceImplTest {

    @Mock
    private FileUpdateMapper fileUpdateMapper;

    @Mock
    private FileUpdateScheduleProperties fileUpdateScheduleProperties;

    @InjectMocks
    private AuthFileUpdateServiceImpl authFileUpdateService;

    @BeforeEach
    void setUp() {
        // 基础设置
    }

    @Test
    @DisplayName("异常场景 - 消息类型为空")
    void testDealFileUpdate_NullSourceCode() {
        // Arrange
        FileUpdateReqDTO fileUpdateReqDTO = new FileUpdateReqDTO();
        fileUpdateReqDTO.setSourceCode(null);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.dealFileUpdate(fileUpdateReqDTO);
        });
    }

    @Test
    @DisplayName("测试基本方法调用")
    void testBasicMethodCalls() {
        // 测试afterPropertiesSet方法
        assertDoesNotThrow(() -> {
            authFileUpdateService.afterPropertiesSet();
        });
    }

    @Test
    @DisplayName("成功场景 - 查询文件更新详情不存在")
    void testDetail_NotFound() {
        // Arrange
        String id = "FILE_UPDATE_001";

        when(fileUpdateMapper.detail(id)).thenReturn(null);

        // Act
        FileUpdateInfoDTO result = authFileUpdateService.detail(id);

        // Assert
        assertNull(result);
        verify(fileUpdateMapper).detail(id);
    }

    @Test
    @DisplayName("异常场景 - 查询详情ID为空")
    void testDetail_BlankId() {
        // Arrange
        String id = "";

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.detail(id);
        });

        verify(fileUpdateMapper, never()).detail(anyString());
    }

    @Test
    @DisplayName("异常场景 - 根据ID重发ID为空")
    void testResendById_BlankId() {
        // Arrange
        String id = "";

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.resendById(id);
        });

        verify(fileUpdateMapper, never()).detail(anyString());
    }

    @Test
    @DisplayName("失败场景 - 根据ID重发消息不存在")
    void testResendById_NotFound() {
        // Arrange
        String id = "FILE_UPDATE_001";

        when(fileUpdateMapper.detail(id)).thenReturn(null);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.resendById(id);
        });

        verify(fileUpdateMapper).detail(id);
    }

    @Test
    @DisplayName("成功场景 - 查询文件更新详情存在")
    void testDetail_Found() {
        // Arrange
        String id = "FILE_UPDATE_001";
        AuthorizationFileUpdate fileUpdate = new AuthorizationFileUpdate();
        fileUpdate.setId(id);
        fileUpdate.setSourceCode("VISA");
        fileUpdate.setCardNumber("1234567890123456");

        when(fileUpdateMapper.detail(id)).thenReturn(fileUpdate);

        // Act
        FileUpdateInfoDTO result = authFileUpdateService.detail(id);

        // Assert
        assertNotNull(result);
        verify(fileUpdateMapper).detail(id);
    }

    @Test
    @DisplayName("成功场景 - 根据ID重发消息存在")
    void testResendById_Found() {
        // Arrange
        String id = "FILE_UPDATE_001";
        AuthorizationFileUpdate fileUpdate = new AuthorizationFileUpdate();
        fileUpdate.setId(id);
        fileUpdate.setSourceCode("VISA");
        fileUpdate.setCardNumber("1234567890123456");

        when(fileUpdateMapper.detail(id)).thenReturn(fileUpdate);

        // Act & Assert
        assertDoesNotThrow(() -> {
            authFileUpdateService.resendById(id);
        });

        verify(fileUpdateMapper).detail(id);
    }

    @Test
    @DisplayName("测试分页查询 - 正常情况")
    void testFindFileUpdateLog_Normal() {
        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class)) {

            // Arrange
            FileUpdateSearchDTO searchDTO = new FileUpdateSearchDTO();
            searchDTO.setId("FILE_001");

            List<AuthorizationFileUpdate> mockList = new ArrayList<>();
            AuthorizationFileUpdate fileUpdate = new AuthorizationFileUpdate();
            fileUpdate.setId("FILE_001");
            mockList.add(fileUpdate);

            when(fileUpdateMapper.selectFileUpdateList(any(FileUpdateSearchDTO.class))).thenReturn(mockList);

            // Act
            PageResultDTO<FileUpdateRespDTO> result = authFileUpdateService.findFileUpdateLog(1, 10, searchDTO);

            // Assert
            assertNotNull(result);
            verify(fileUpdateMapper).selectFileUpdateList(searchDTO);
        }
    }

    @Test
    @DisplayName("测试分页查询 - 空参数")
    void testFindFileUpdateLog_NullParam() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            authFileUpdateService.findFileUpdateLog(1, 10, null);
        });
    }

    @Test
    @DisplayName("测试dealFileUpdate - 正常处理")
    void testDealFileUpdate_Normal() {
        // Arrange
        FileUpdateReqDTO fileUpdateReqDTO = new FileUpdateReqDTO();
        fileUpdateReqDTO.setSourceCode("V");
        fileUpdateReqDTO.setCardNumber("1234567890123456");
        fileUpdateReqDTO.setAction("A");

        // Act & Assert
        assertDoesNotThrow(() -> {
            authFileUpdateService.dealFileUpdate(fileUpdateReqDTO);
        });
    }

    @Test
    @DisplayName("测试dealFileUpdate - 不同卡类型")
    void testDealFileUpdate_DifferentCardType() {
        // Arrange
        FileUpdateReqDTO fileUpdateReqDTO = new FileUpdateReqDTO();
        fileUpdateReqDTO.setSourceCode("M");
        fileUpdateReqDTO.setCardNumber("****************");
        fileUpdateReqDTO.setAction("C");

        // Act & Assert
        assertDoesNotThrow(() -> {
            authFileUpdateService.dealFileUpdate(fileUpdateReqDTO);
        });
    }

    @Test
    @DisplayName("测试方法签名验证")
    void testMethodSignatures() throws NoSuchMethodException {
        // 验证主要方法存在
        assertNotNull(AuthFileUpdateServiceImpl.class.getDeclaredMethod("detail", String.class));
        assertNotNull(AuthFileUpdateServiceImpl.class.getDeclaredMethod("resendById", String.class));
        assertNotNull(AuthFileUpdateServiceImpl.class.getDeclaredMethod("findFileUpdateLog", Integer.class, Integer.class, FileUpdateSearchDTO.class));
        assertNotNull(AuthFileUpdateServiceImpl.class.getDeclaredMethod("dealFileUpdate", FileUpdateReqDTO.class));
    }

    @Test
    @DisplayName("测试依赖注入验证")
    void testDependencyInjection() {
        // 验证依赖注入
        assertNotNull(authFileUpdateService);
        assertNotNull(fileUpdateMapper);
        assertNotNull(fileUpdateScheduleProperties);
    }

    @Test
    @DisplayName("测试异常处理 - detail方法null参数")
    void testDetail_NullId() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.detail(null);
        });
    }

    @Test
    @DisplayName("测试异常处理 - resendById方法null参数")
    void testResendById_NullId() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.resendById(null);
        });
    }

    @Test
    @DisplayName("测试边界值 - 空字符串参数")
    void testBoundaryValues_EmptyStrings() {
        // 测试空字符串参数
        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.detail("   ");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.resendById("   ");
        });
    }

    @Test
    @DisplayName("测试多次调用相同方法")
    void testMultipleCalls() {
        // Arrange
        String id = "FILE_UPDATE_001";
        AuthorizationFileUpdate fileUpdate = new AuthorizationFileUpdate();
        fileUpdate.setId(id);

        when(fileUpdateMapper.detail(id)).thenReturn(fileUpdate);

        // Act
        authFileUpdateService.detail(id);
        authFileUpdateService.detail(id);

        // Assert
        verify(fileUpdateMapper, times(2)).detail(id);
    }
}