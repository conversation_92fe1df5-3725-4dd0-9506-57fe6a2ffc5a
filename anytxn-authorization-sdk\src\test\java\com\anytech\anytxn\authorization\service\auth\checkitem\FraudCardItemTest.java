package com.anytech.anytxn.authorization.service.auth.checkitem;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.dto.ParmAuthCheckControlDTO;
import com.anytech.anytxn.authorization.mapper.fraudcard.FraudCardInfoSelfMapper;
import com.anytech.anytxn.authorization.base.domain.model.FraudCardInfo;
import com.anytech.anytxn.authorization.base.enums.StatusEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * @description 欺诈卡检查项测试类
 * <AUTHOR>
 * @date 2025/07/10
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
class FraudCardItemTest {

    @InjectMocks
    private FraudCardItem fraudCardItem;

    @Mock
    private FraudCardInfoSelfMapper fraudCardInfoSelfMapper;

    @Mock
    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;

    @Mock
    private ParmAuthCheckControlDTO parmAuthCheckControlDTO;

    @Mock
    private AuthRecordedDTO authRecordedDTO;

    @Mock
    private CardAuthorizationDTO cardAuthorizationDTO;

    @BeforeEach
    void setUp() {
        when(authorizationCheckProcessingPayload.getAuthRecordedDTO()).thenReturn(authRecordedDTO);
        when(authorizationCheckProcessingPayload.getCardAuthorizationDTO()).thenReturn(cardAuthorizationDTO);
        when(cardAuthorizationDTO.getOrganizationNumber()).thenReturn("001");
        when(cardAuthorizationDTO.getCardNumber()).thenReturn("1234567890123456");
    }

    @Test
    @DisplayName("Check should approve when fraud card info does not exist")
    void testCheck_FraudCardInfoNotExists() {
        // Arrange
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(null);

        // Act
        Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should reject when fraud card is effective and not expired")
    void testCheck_FraudCardEffectiveAndNotExpired() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            FraudCardInfo fraudCardInfo = new FraudCardInfo();
            fraudCardInfo.setStatus(StatusEnum.EFFECTIVE.getCode());
            fraudCardInfo.setExpireDate(LocalDate.now().plusDays(1));
            when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(fraudCardInfo);

            // Act
            Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthResponseCodeEnum.SUSPECTED_FRAUD_2.getCode(), result);
            verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
        }
    }

    @Test
    @DisplayName("Check should approve when fraud card is effective but expired")
    void testCheck_FraudCardEffectiveButExpired() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            FraudCardInfo fraudCardInfo = new FraudCardInfo();
            fraudCardInfo.setStatus(StatusEnum.EFFECTIVE.getCode());
            fraudCardInfo.setExpireDate(LocalDate.now().minusDays(1));
            when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(fraudCardInfo);

            // Act
            Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
            verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
            verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        }
    }

    @Test
    @DisplayName("Check should approve when fraud card is not effective")
    void testCheck_FraudCardNotEffective() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            FraudCardInfo fraudCardInfo = new FraudCardInfo();
            fraudCardInfo.setStatus(StatusEnum.INVALID.getCode());
            fraudCardInfo.setExpireDate(LocalDate.now().plusDays(1));
            when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(fraudCardInfo);

            // Act
            Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
            verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
            verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        }
    }

    @Test
    @DisplayName("Check should return exception when fraud card status is null")
    void testCheck_FraudCardStatusNull() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            FraudCardInfo fraudCardInfo = new FraudCardInfo();
            fraudCardInfo.setStatus(null);
            fraudCardInfo.setExpireDate(LocalDate.now().plusDays(1));
            when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(fraudCardInfo);

            // Act
            Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode(), result);
        }
    }

    @Test
    @DisplayName("Check should return exception when fraud card status is empty")
    void testCheck_FraudCardStatusEmpty() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            FraudCardInfo fraudCardInfo = new FraudCardInfo();
            fraudCardInfo.setStatus("");
            fraudCardInfo.setExpireDate(LocalDate.now().plusDays(1));
            when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(fraudCardInfo);

            // Act
            Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode(), result);
        }
    }

    @Test
    @DisplayName("Check should return exception when fraud card expire date is null")
    void testCheck_FraudCardExpireDateNull() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            FraudCardInfo fraudCardInfo = new FraudCardInfo();
            fraudCardInfo.setStatus(StatusEnum.EFFECTIVE.getCode());
            fraudCardInfo.setExpireDate(null);
            when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(fraudCardInfo);

            // Act
            Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode(), result);
        }
    }

    @Test
    @DisplayName("Check should approve when fraud card expires today")
    void testCheck_FraudCardExpiresOnToday() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            FraudCardInfo fraudCardInfo = new FraudCardInfo();
            fraudCardInfo.setStatus(StatusEnum.EFFECTIVE.getCode());
            fraudCardInfo.setExpireDate(LocalDate.now());
            when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(fraudCardInfo);

            // Act
            Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
            verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
            verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        }
    }

    @Test
    @DisplayName("Check should handle unknown status")
    void testCheck_FraudCardUnknownStatus() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            FraudCardInfo fraudCardInfo = new FraudCardInfo();
            fraudCardInfo.setStatus("UNKNOWN_STATUS");
            fraudCardInfo.setExpireDate(LocalDate.now().plusDays(1));
            when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(fraudCardInfo);

            // Act
            Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
            verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
            verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        }
    }

    @Test
    @DisplayName("Check should handle database exception")
    void testCheck_DatabaseException() {
        // Arrange
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString()))
                .thenThrow(new RuntimeException("Database error"));

        // Act
        Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode(), result);
    }

    @Test
    @DisplayName("AuthFraudCardCheck should delegate to check method")
    void testAuthFraudCardCheck() {
        // Arrange
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(null);

        // Act
        int result = fraudCardItem.authFraudCardCheck(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should handle null organization number")
    void testCheck_NullOrganizationNumber() {
        // Arrange
        when(cardAuthorizationDTO.getOrganizationNumber()).thenReturn(null);
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(isNull(), anyString())).thenReturn(null);

        // Act
        Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should handle null card number")
    void testCheck_NullCardNumber() {
        // Arrange
        when(cardAuthorizationDTO.getCardNumber()).thenReturn(null);
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), isNull())).thenReturn(null);

        // Act
        Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should handle empty organization number")
    void testCheck_EmptyOrganizationNumber() {
        // Arrange
        when(cardAuthorizationDTO.getOrganizationNumber()).thenReturn("");
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(null);

        // Act
        Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should handle empty card number")
    void testCheck_EmptyCardNumber() {
        // Arrange
        when(cardAuthorizationDTO.getCardNumber()).thenReturn("");
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString())).thenReturn(null);

        // Act
        Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("测试类注解和依赖注入验证")
    void testClassAnnotationsAndDI() {
        // 验证依赖注入
        assertNotNull(fraudCardItem);
        assertNotNull(fraudCardInfoSelfMapper);

        // 验证类信息
        assertEquals("FraudCardItem", FraudCardItem.class.getSimpleName());
        assertFalse(FraudCardItem.class.isInterface());
    }

    @Test
    @DisplayName("测试方法签名验证")
    void testMethodSignatures() throws NoSuchMethodException {
        // 验证主要方法存在
        assertNotNull(FraudCardItem.class.getDeclaredMethod("check",
                AuthorizationCheckProcessingPayload.class, ParmAuthCheckControlDTO.class));
        assertNotNull(FraudCardItem.class.getDeclaredMethod("authFraudCardCheck",
                AuthorizationCheckProcessingPayload.class, ParmAuthCheckControlDTO.class));
    }

    @Test
    @DisplayName("测试多次调用相同方法")
    void testMultipleCalls() {
        // Arrange
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString()))
                .thenReturn(null);

        // Act
        fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);
        fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        verify(fraudCardInfoSelfMapper, times(2))
                .selectByOrgAndFraudNumber(anyString(), anyString());
        verify(parmAuthCheckControlDTO, times(2)).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("测试边界值 - 过期日期边界")
    void testCheck_ExpireDateBoundary() {
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            // Arrange - 测试过期日期为明天
            FraudCardInfo fraudCardInfo = new FraudCardInfo();
            fraudCardInfo.setStatus(StatusEnum.EFFECTIVE.getCode());
            fraudCardInfo.setExpireDate(LocalDate.now().plusDays(1));
            when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString()))
                    .thenReturn(fraudCardInfo);

            // Act
            Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthResponseCodeEnum.SUSPECTED_FRAUD_2.getCode(), result);
            verify(authRecordedDTO).setAuthFraudFileResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
        }
    }

    @Test
    @DisplayName("测试性能 - 大量调用")
    void testCheck_Performance() {
        // Arrange
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString()))
                .thenReturn(null);

        // Act - 测试多次调用的性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);
        }
        long endTime = System.currentTimeMillis();

        // Assert - 100次调用应该在合理时间内完成
        assertTrue(endTime - startTime < 1000, "100次方法调用应该在1秒内完成");
        verify(fraudCardInfoSelfMapper, times(100))
                .selectByOrgAndFraudNumber(anyString(), anyString());
    }

    @Test
    @DisplayName("测试参数传递验证")
    void testCheck_ParameterPassing() {
        // Arrange
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString()))
                .thenAnswer(invocation -> {
                    // 验证参数传递正确
                    String org = invocation.getArgument(0);
                    String cardNumber = invocation.getArgument(1);

                    assertEquals("001", org);
                    assertEquals("1234567890123456", cardNumber);
                    return null;
                });

        // Act
        fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        verify(fraudCardInfoSelfMapper).selectByOrgAndFraudNumber("001", "1234567890123456");
    }

    @Test
    @DisplayName("测试返回值类型验证")
    void testCheck_ReturnType() {
        // Arrange
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString()))
                .thenReturn(null);

        // Act
        Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof Integer);
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
    }

    @Test
    @DisplayName("测试异常处理增强")
    void testCheck_ExceptionHandlingEnhanced() {
        // 测试各种异常类型
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString()))
                .thenThrow(new IllegalArgumentException("Invalid argument"))
                .thenThrow(new IllegalStateException("Invalid state"))
                .thenThrow(new UnsupportedOperationException("Unsupported operation"));

        // Act & Assert
        assertEquals(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode(),
                fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO));
        assertEquals(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode(),
                fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO));
        assertEquals(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode(),
                fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO));
    }

    @Test
    @DisplayName("测试Mock验证增强")
    void testCheck_MockVerificationEnhanced() {
        // Arrange
        when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString()))
                .thenReturn(null);

        // Act
        fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert - 详细验证Mock交互
        verify(fraudCardInfoSelfMapper, times(1))
                .selectByOrgAndFraudNumber(anyString(), anyString());
        verify(fraudCardInfoSelfMapper, never())
                .selectByOrgAndFraudNumber(isNull(), isNull());
        verify(fraudCardInfoSelfMapper, atLeastOnce())
                .selectByOrgAndFraudNumber(anyString(), anyString());
        verify(fraudCardInfoSelfMapper, atMost(1))
                .selectByOrgAndFraudNumber(anyString(), anyString());
        verifyNoMoreInteractions(fraudCardInfoSelfMapper);
    }

    @Test
    @DisplayName("测试特殊卡号格式")
    void testCheck_SpecialCardNumberFormats() {
        // 测试不同格式的卡号
        String[] cardNumbers = {
            "****************",  // 标准Visa
            "****************",  // 标准MasterCard
            "***************",   // American Express
            "****************",  // Discover
            "1234567890123456789", // 超长卡号
            "123456",            // 短卡号
            "0000000000000000"   // 全零卡号
        };

        for (String cardNumber : cardNumbers) {
            // Arrange
            when(cardAuthorizationDTO.getCardNumber()).thenReturn(cardNumber);
            when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), eq(cardNumber)))
                    .thenReturn(null);

            // Act
            Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        }
    }

    @Test
    @DisplayName("测试不同状态枚举值")
    void testCheck_DifferentStatusEnums() {
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            // 测试所有可能的状态值
            String[] statuses = {
                StatusEnum.EFFECTIVE.getCode(),
                StatusEnum.INVALID.getCode(),
                "PENDING",
                "SUSPENDED",
                "UNKNOWN"
            };

            for (String status : statuses) {
                // Arrange
                FraudCardInfo fraudCardInfo = new FraudCardInfo();
                fraudCardInfo.setStatus(status);
                fraudCardInfo.setExpireDate(LocalDate.now().plusDays(1));
                when(fraudCardInfoSelfMapper.selectByOrgAndFraudNumber(anyString(), anyString()))
                        .thenReturn(fraudCardInfo);

                // Act
                Integer result = fraudCardItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

                // Assert
                if (StatusEnum.EFFECTIVE.getCode().equals(status)) {
                    assertEquals(AuthResponseCodeEnum.SUSPECTED_FRAUD_2.getCode(), result);
                } else {
                    assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
                }
            }
        }
    }
}