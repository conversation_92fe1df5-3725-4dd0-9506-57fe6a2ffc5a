package com.anytech.anytxn.authorization.service.manager;

import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.Serializable;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.mockStatic;

/**
 * AuthThreadLocalManager单元测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-15
 */
@ExtendWith(MockitoExtension.class)
class AuthThreadLocalManagerTest {

    private Serializable testSerializable;
    private AccountStatisticsInfo testAccountStatisticsInfo;
    private ISO8583DTO testISO8583DTO;
    private AuthRecordedDTO testAuthRecordedDTO;

    @BeforeEach
    void setUp() {
        // 初始化OrgNumberUtils静态字段，避免NullPointerException
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        lenient().when(OrgNumberUtils.getOrg()).thenReturn("001");
        lenient().when(OrgNumberUtils.orgNumberUtil.getBatchOrg()).thenReturn("001");

        // 清理ThreadLocal以确保测试隔离
        AuthThreadLocalManager.remove();

        // 初始化测试数据
        testSerializable = "test-serializable-data";

        // 注意：testAccountStatisticsInfo和testAuthRecordedDTO将在各个测试方法中初始化
        // 以避免OrgNumberUtils的null问题
    }

    private void initializeTestData() {
        testAccountStatisticsInfo = new AccountStatisticsInfo();

        testAuthRecordedDTO = new AuthRecordedDTO();
        testAuthRecordedDTO.setAuthCardNumber("****************");
        testAuthRecordedDTO.setAuthCustomerId("*********");
    }

    @Test
    @DisplayName("测试请求数据的存储和获取")
    void testReqDataOperations() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Act - 存储数据
            AuthThreadLocalManager.buildReqData(testSerializable);

            // Assert - 获取数据
            Serializable result = AuthThreadLocalManager.getReqData();
            assertEquals(testSerializable, result);
        }
    }

    @Test
    @DisplayName("测试账户统计信息的存储和获取")
    void testAccountStatisticsInfoOperations() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Act - 存储数据
            AuthThreadLocalManager.setAccountStatisticsInfoThreadLocal(testAccountStatisticsInfo);

            // Assert - 获取数据
            AccountStatisticsInfo result = AuthThreadLocalManager.getAccountStatisticsInfo();
            assertEquals(testAccountStatisticsInfo, result);
        }
    }

    @Test
    @DisplayName("测试ISO8583DTO的存储和获取")
    void testISO8583DTOOperations() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            testISO8583DTO = new ISO8583DTO();

            // Act - 存储数据
            AuthThreadLocalManager.setIso8583dtoThreadLocal(testISO8583DTO);

            // Assert - 获取数据
            ISO8583DTO result = AuthThreadLocalManager.getIso8583dtoThreadLocal();
            assertEquals(testISO8583DTO, result);
        }
    }

    @Test
    @DisplayName("测试AuthRecordedDTO的存储和获取")
    void testAuthRecordedDTOOperations() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Act - 存储数据
            AuthThreadLocalManager.setAuthRecordeddtoThreadLocal(testAuthRecordedDTO);

            // Assert - 获取数据
            AuthRecordedDTO result = AuthThreadLocalManager.getAuthRecordeddtoThreadLocal();
            assertEquals(testAuthRecordedDTO, result);
            assertEquals("****************", result.getAuthCardNumber());
            assertEquals("*********", result.getAuthCustomerId());
        }
    }

    @Test
    @DisplayName("测试TraceId的存储和获取")
    void testTraceIdOperations() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            String testTraceId = "trace-id-12345";

            // Act - 存储数据
            AuthThreadLocalManager.setTraceId(testTraceId);

            // Assert - 获取数据
            String result = AuthThreadLocalManager.getTraceId();
            assertEquals(testTraceId, result);
        }
    }

    @Test
    @DisplayName("测试ThreadLocal清理功能")
    void testRemoveOperation() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            testISO8583DTO = new ISO8583DTO();

            // 先设置所有数据
            AuthThreadLocalManager.buildReqData(testSerializable);
            AuthThreadLocalManager.setAccountStatisticsInfoThreadLocal(testAccountStatisticsInfo);
            AuthThreadLocalManager.setIso8583dtoThreadLocal(testISO8583DTO);
            AuthThreadLocalManager.setAuthRecordeddtoThreadLocal(testAuthRecordedDTO);
            AuthThreadLocalManager.setTraceId("test-trace-id");

            // 验证数据已设置
            assertNotNull(AuthThreadLocalManager.getReqData());
            assertNotNull(AuthThreadLocalManager.getAccountStatisticsInfo());
            assertNotNull(AuthThreadLocalManager.getIso8583dtoThreadLocal());
            assertNotNull(AuthThreadLocalManager.getAuthRecordeddtoThreadLocal());
            assertNotNull(AuthThreadLocalManager.getTraceId());

            // Act - 清理所有ThreadLocal数据
            AuthThreadLocalManager.remove();

            // Assert - 验证所有数据已清理
            assertNull(AuthThreadLocalManager.getReqData());
            assertNull(AuthThreadLocalManager.getAccountStatisticsInfo());
            assertNull(AuthThreadLocalManager.getIso8583dtoThreadLocal());
            assertNull(AuthThreadLocalManager.getAuthRecordeddtoThreadLocal());
            assertNull(AuthThreadLocalManager.getTraceId());
        }
    }

    @Test
    @DisplayName("测试多线程环境下的数据隔离")
    void testThreadIsolation() throws InterruptedException {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            final String[] thread1Result = new String[1];
            final String[] thread2Result = new String[1];

            Thread thread1 = new Thread(() -> {
                AuthThreadLocalManager.setTraceId("thread1-trace-id");
                try {
                    Thread.sleep(100); // 让线程2有机会设置数据
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                thread1Result[0] = AuthThreadLocalManager.getTraceId();
            });

            Thread thread2 = new Thread(() -> {
                AuthThreadLocalManager.setTraceId("thread2-trace-id");
                try {
                    Thread.sleep(100); // 让线程1有机会设置数据
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                thread2Result[0] = AuthThreadLocalManager.getTraceId();
            });

            // Act
            thread1.start();
            thread2.start();

            thread1.join();
            thread2.join();

            // Assert - 验证每个线程的数据是隔离的
            assertEquals("thread1-trace-id", thread1Result[0]);
            assertEquals("thread2-trace-id", thread2Result[0]);
        }
    }

    @Test
    @DisplayName("测试空值处理")
    void testNullValueHandling() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Act & Assert - 测试设置null值
            AuthThreadLocalManager.buildReqData(null);
            assertNull(AuthThreadLocalManager.getReqData());

            AuthThreadLocalManager.setAccountStatisticsInfoThreadLocal(null);
            assertNull(AuthThreadLocalManager.getAccountStatisticsInfo());

            AuthThreadLocalManager.setIso8583dtoThreadLocal(null);
            assertNull(AuthThreadLocalManager.getIso8583dtoThreadLocal());

            AuthThreadLocalManager.setAuthRecordeddtoThreadLocal(null);
            assertNull(AuthThreadLocalManager.getAuthRecordeddtoThreadLocal());

            AuthThreadLocalManager.setTraceId(null);
            assertNull(AuthThreadLocalManager.getTraceId());
        }
    }

    @Test
    @DisplayName("验证AuthThreadLocalManager类存在且为工具类")
    void testAuthThreadLocalManagerClassExists() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Assert - 验证类存在
            assertNotNull(AuthThreadLocalManager.class);
            assertEquals("AuthThreadLocalManager", AuthThreadLocalManager.class.getSimpleName());

            // 验证是工具类（所有方法都是静态的）
            assertTrue(java.lang.reflect.Modifier.isPublic(AuthThreadLocalManager.class.getModifiers()));
        }
    }

    @Test
    @DisplayName("测试方法签名验证")
    void testMethodSignatures() throws NoSuchMethodException {
        // 验证所有公共方法存在
        assertNotNull(AuthThreadLocalManager.class.getDeclaredMethod("buildReqData", Serializable.class));
        assertNotNull(AuthThreadLocalManager.class.getDeclaredMethod("getReqData"));
        assertNotNull(AuthThreadLocalManager.class.getDeclaredMethod("setAccountStatisticsInfoThreadLocal", AccountStatisticsInfo.class));
        assertNotNull(AuthThreadLocalManager.class.getDeclaredMethod("getAccountStatisticsInfo"));
        assertNotNull(AuthThreadLocalManager.class.getDeclaredMethod("setIso8583dtoThreadLocal", ISO8583DTO.class));
        assertNotNull(AuthThreadLocalManager.class.getDeclaredMethod("getIso8583dtoThreadLocal"));
        assertNotNull(AuthThreadLocalManager.class.getDeclaredMethod("setAuthRecordeddtoThreadLocal", AuthRecordedDTO.class));
        assertNotNull(AuthThreadLocalManager.class.getDeclaredMethod("getAuthRecordeddtoThreadLocal"));
        assertNotNull(AuthThreadLocalManager.class.getDeclaredMethod("setTraceId", String.class));
        assertNotNull(AuthThreadLocalManager.class.getDeclaredMethod("getTraceId"));
        assertNotNull(AuthThreadLocalManager.class.getDeclaredMethod("remove"));
    }

    @Test
    @DisplayName("测试数据覆盖和更新")
    void testDataOverwriteAndUpdate() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Act - 设置初始数据
            AuthThreadLocalManager.setTraceId("initial-trace-id");
            assertEquals("initial-trace-id", AuthThreadLocalManager.getTraceId());

            // Act - 覆盖数据
            AuthThreadLocalManager.setTraceId("updated-trace-id");
            assertEquals("updated-trace-id", AuthThreadLocalManager.getTraceId());

            // Act - 再次覆盖为null
            AuthThreadLocalManager.setTraceId(null);
            assertNull(AuthThreadLocalManager.getTraceId());
        }
    }

    @Test
    @DisplayName("测试复杂对象的存储和获取")
    void testComplexObjectOperations() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // 创建复杂的ISO8583DTO对象
            ISO8583DTO complexISO = new ISO8583DTO();
            complexISO.setMTI("0100");

            // 创建复杂的AuthRecordedDTO对象
            AuthRecordedDTO complexAuth = new AuthRecordedDTO();
            complexAuth.setAuthCardNumber("****************");
            complexAuth.setAuthCustomerId("987654321");

            // Act - 存储复杂对象
            AuthThreadLocalManager.setIso8583dtoThreadLocal(complexISO);
            AuthThreadLocalManager.setAuthRecordeddtoThreadLocal(complexAuth);

            // Assert - 验证复杂对象的完整性
            ISO8583DTO retrievedISO = AuthThreadLocalManager.getIso8583dtoThreadLocal();
            AuthRecordedDTO retrievedAuth = AuthThreadLocalManager.getAuthRecordeddtoThreadLocal();

            assertNotNull(retrievedISO);
            assertEquals("0100", retrievedISO.getMTI());

            assertNotNull(retrievedAuth);
            assertEquals("****************", retrievedAuth.getAuthCardNumber());
            assertEquals("987654321", retrievedAuth.getAuthCustomerId());
        }
    }

    @Test
    @DisplayName("测试性能 - 大量操作")
    void testPerformance() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Act - 测试大量设置和获取操作的性能
            long startTime = System.currentTimeMillis();

            for (int i = 0; i < 1000; i++) {
                AuthThreadLocalManager.setTraceId("trace-id-" + i);
                String result = AuthThreadLocalManager.getTraceId();
                assertEquals("trace-id-" + i, result);
            }

            long endTime = System.currentTimeMillis();

            // Assert - 1000次操作应该在合理时间内完成
            assertTrue(endTime - startTime < 1000, "1000次ThreadLocal操作应该在1秒内完成");
        }
    }

    @Test
    @DisplayName("测试内存清理验证")
    void testMemoryCleanup() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Act - 设置大量数据然后清理
            for (int i = 0; i < 100; i++) {
                AuthThreadLocalManager.setTraceId("trace-id-" + i);
                AuthThreadLocalManager.buildReqData("data-" + i);
                AuthThreadLocalManager.setAccountStatisticsInfoThreadLocal(testAccountStatisticsInfo);
                AuthThreadLocalManager.remove(); // 每次都清理
            }

            // Assert - 验证最终状态为空
            assertNull(AuthThreadLocalManager.getTraceId());
            assertNull(AuthThreadLocalManager.getReqData());
            assertNull(AuthThreadLocalManager.getAccountStatisticsInfo());
        }
    }

    @Test
    @DisplayName("测试边界值处理")
    void testBoundaryValues() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Act & Assert - 测试空字符串
            AuthThreadLocalManager.setTraceId("");
            assertEquals("", AuthThreadLocalManager.getTraceId());

            // Act & Assert - 测试很长的字符串
            String longString = "a".repeat(10000);
            AuthThreadLocalManager.setTraceId(longString);
            assertEquals(longString, AuthThreadLocalManager.getTraceId());

            // Act & Assert - 测试特殊字符
            String specialString = "!@#$%^&*()_+-=[]{}|;':\",./<>?`~";
            AuthThreadLocalManager.setTraceId(specialString);
            assertEquals(specialString, AuthThreadLocalManager.getTraceId());
        }
    }

    @Test
    @DisplayName("测试异常情况处理")
    void testExceptionHandling() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Act & Assert - 在没有设置数据的情况下获取数据
            AuthThreadLocalManager.remove(); // 确保清空
            assertNull(AuthThreadLocalManager.getReqData());
            assertNull(AuthThreadLocalManager.getAccountStatisticsInfo());
            assertNull(AuthThreadLocalManager.getIso8583dtoThreadLocal());
            assertNull(AuthThreadLocalManager.getAuthRecordeddtoThreadLocal());
            assertNull(AuthThreadLocalManager.getTraceId());

            // Act & Assert - 多次调用remove不应该出错
            assertDoesNotThrow(() -> {
                AuthThreadLocalManager.remove();
                AuthThreadLocalManager.remove();
                AuthThreadLocalManager.remove();
            });
        }
    }

    @Test
    @DisplayName("测试类型安全性")
    void testTypeSafety() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Act - 设置不同类型的Serializable对象
            String stringData = "string-data";
            Integer integerData = 12345;

            AuthThreadLocalManager.buildReqData(stringData);
            assertEquals(stringData, AuthThreadLocalManager.getReqData());

            AuthThreadLocalManager.buildReqData(integerData);
            assertEquals(integerData, AuthThreadLocalManager.getReqData());

            // Assert - 验证类型正确
            assertTrue(AuthThreadLocalManager.getReqData() instanceof Integer);
            assertEquals(12345, (Integer) AuthThreadLocalManager.getReqData());
        }
    }
}
