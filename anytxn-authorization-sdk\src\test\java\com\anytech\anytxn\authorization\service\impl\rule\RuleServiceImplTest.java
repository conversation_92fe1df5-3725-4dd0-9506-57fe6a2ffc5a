/**
 * @description 规则调用服务实现类的单元测试
 * <AUTHOR>
 * @date 2025/07/07
 * @version 1.0
 * @AI 已复核
 */
package com.anytech.anytxn.authorization.service.impl.rule;

import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.service.rule.RuleServiceImpl;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.dao.authorization.mapper.AuthRuleLogMapper;
import com.anytech.anytxn.business.dao.authorization.model.AuthRuleLog;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.common.rule.matcher.RuleMatcherManager;
import com.anytech.anytxn.common.rule.matcher.TxnRuleMatcher;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("RuleServiceImpl单元测试")
class RuleServiceImplTest {

    @Mock
    private AuthRuleLogMapper authRuleLogMapper;

    @Mock
    private SequenceIdGen sequenceIdGen;

    @Mock
    private TxnRuleMatcher txnRuleMatcher;

    @InjectMocks
    private RuleServiceImpl ruleService;

    private DataInputDTO dataInputDTO;
    private Map<String, Object> ruleResult;

    @BeforeEach
    void setUp() {
        dataInputDTO = new DataInputDTO();
        dataInputDTO.setRuleType("authorization_check_rule");
        dataInputDTO.setOrganizationNumber("000");
        
        Map<String, Object> input = new HashMap<>();
        input.put("globalFlowNumber", "GF001");
        dataInputDTO.setInput(input);

        ruleResult = new HashMap<>();
        ruleResult.put("result", "success");
        ruleResult.put("code", "0000");
        ruleResult.put(TxnRuleMatcher.EXE_TYPE, "1");
        ruleResult.put(TxnRuleMatcher.RULE_ID, "RULE001");
        ruleResult.put(TxnRuleMatcher.RULE_TYPE, "authorization_check_rule");
    }

    @Test
    @DisplayName("测试执行规则-授权检查规则-正常情况")
    void testExecuteRule_AuthorizationCheckRule_Success() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);
            
            when(txnRuleMatcher.execute(eq(dataInputDTO), eq(true))).thenReturn(ruleResult);

            // 执行方法
            Map<String, String> result = ruleService.executeRule(dataInputDTO);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result).hasSize(5);
            assertThat(result.get("result")).isEqualTo("success");
            assertThat(result.get("code")).isEqualTo("0000");

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("authorization_check_rule", "000"));
            verify(txnRuleMatcher).execute(dataInputDTO, true);
        }
    }

    @Test
    @DisplayName("测试执行规则-其他类型规则-正常情况")
    void testExecuteRule_OtherRule_Success() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            dataInputDTO.setRuleType("other_rule");
            
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);
            
            when(txnRuleMatcher.execute(dataInputDTO)).thenReturn(ruleResult);

            // 执行方法
            Map<String, String> result = ruleService.executeRule(dataInputDTO);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result).hasSize(5);
            assertThat(result.get("result")).isEqualTo("success");
            assertThat(result.get("code")).isEqualTo("0000");

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("other_rule", "000"));
            verify(txnRuleMatcher).execute(dataInputDTO);
        }
    }

    @Test
    @DisplayName("测试执行规则-未匹配到规则引擎")
    void testExecuteRule_NoMatcherFound() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(null);

            // 执行方法
            Map<String, String> result = ruleService.executeRule(dataInputDTO);

            // 验证结果
            assertThat(result).isNull();

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("authorization_check_rule", "000"));
        }
    }

    @Test
    @DisplayName("测试执行规则-规则执行异常")
    void testExecuteRule_ExecuteException() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);

            when(txnRuleMatcher.execute(eq(dataInputDTO), eq(true)))
                    .thenThrow(new RuntimeException("规则执行异常"));

            // 执行方法并验证异常
            assertThatThrownBy(() -> ruleService.executeRule(dataInputDTO))
                    .isInstanceOf(AnyTxnAuthException.class)
                    .satisfies(exception -> {
                        AnyTxnAuthException authException = (AnyTxnAuthException) exception;
                        assertThat(authException.getErrCode()).isEqualTo(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR.getCode());
                    });

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("authorization_check_rule", "000"));
            verify(txnRuleMatcher).execute(dataInputDTO, true);
        }
    }

    @Test
    @DisplayName("测试执行规则-结果包含null值")
    void testExecuteRule_ResultWithNullValue() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            Map<String, Object> resultWithNull = new HashMap<>();
            resultWithNull.put("result", "success");
            resultWithNull.put("nullValue", null);
            resultWithNull.put("emptyValue", "");
            
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);
            
            when(txnRuleMatcher.execute(eq(dataInputDTO), eq(true))).thenReturn(resultWithNull);

            // 执行方法
            Map<String, String> result = ruleService.executeRule(dataInputDTO);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result).hasSize(3);
            assertThat(result.get("result")).isEqualTo("success");
            assertThat(result.get("nullValue")).isEqualTo("");
            assertThat(result.get("emptyValue")).isEqualTo("");

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("authorization_check_rule", "000"));
            verify(txnRuleMatcher).execute(dataInputDTO, true);
        }
    }

    @Test
    @DisplayName("测试执行规则-结果包含非字符串值")
    void testExecuteRule_ResultWithNonStringValue() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            Map<String, Object> resultWithObjects = new HashMap<>();
            resultWithObjects.put("intValue", 123);
            resultWithObjects.put("boolValue", true);
            resultWithObjects.put("doubleValue", 45.67);
            
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);
            
            when(txnRuleMatcher.execute(eq(dataInputDTO), eq(true))).thenReturn(resultWithObjects);

            // 执行方法
            Map<String, String> result = ruleService.executeRule(dataInputDTO);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result).hasSize(3);
            assertThat(result.get("intValue")).isEqualTo("123");
            assertThat(result.get("boolValue")).isEqualTo("true");
            assertThat(result.get("doubleValue")).isEqualTo("45.67");

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("authorization_check_rule", "000"));
            verify(txnRuleMatcher).execute(dataInputDTO, true);
        }
    }

    @Test
    @DisplayName("测试执行规则-空输入数据")
    void testExecuteRule_EmptyInput() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            DataInputDTO emptyInput = new DataInputDTO();
            emptyInput.setRuleType("test_rule");
            emptyInput.setOrganizationNumber("001");
            emptyInput.setInput(new HashMap<>());
            
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);
            
            when(txnRuleMatcher.execute(emptyInput)).thenReturn(new HashMap<>());

            // 执行方法
            Map<String, String> result = ruleService.executeRule(emptyInput);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result).isEmpty();

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("test_rule", "001"));
            verify(txnRuleMatcher).execute(emptyInput);
        }
    }

    @Test
    @DisplayName("测试获取过期时间-抛出IOException")
    void testGetExpireTime_ThrowsIOException() {
        // 执行方法并验证异常
        assertThatThrownBy(() -> ruleService.getExpireTime())
                .isInstanceOf(IOException.class);
    }

    @Test
    @DisplayName("测试执行规则-获取Matcher异常")
    void testExecuteRule_GetMatcherException() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenThrow(new RuntimeException("获取Matcher异常"));

            // 执行方法并验证异常
            assertThatThrownBy(() -> ruleService.executeRule(dataInputDTO))
                    .isInstanceOf(AnyTxnAuthException.class)
                    .satisfies(exception -> {
                        AnyTxnAuthException authException = (AnyTxnAuthException) exception;
                        assertThat(authException.getErrCode()).isEqualTo(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR.getCode());
                    });

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("authorization_check_rule", "000"));
        }
    }

    @Test
    @DisplayName("测试执行规则-输入数据为空")
    void testExecuteRule_NullInput() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            DataInputDTO nullInputDTO = new DataInputDTO();
            nullInputDTO.setRuleType("test_rule");
            nullInputDTO.setOrganizationNumber("000");
            nullInputDTO.setInput(null);

            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);

            when(txnRuleMatcher.execute(nullInputDTO)).thenReturn(ruleResult);

            // 执行方法
            Map<String, String> result = ruleService.executeRule(nullInputDTO);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result).hasSize(5);

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("test_rule", "000"));
            verify(txnRuleMatcher).execute(nullInputDTO);
        }
    }

    @Test
    @DisplayName("测试方法签名验证")
    void testMethodSignatures() throws NoSuchMethodException {
        // 验证主要方法存在
        assertThat(RuleServiceImpl.class.getDeclaredMethod("executeRule", DataInputDTO.class)).isNotNull();
        assertThat(RuleServiceImpl.class.getDeclaredMethod("getExpireTime")).isNotNull();
    }

    @Test
    @DisplayName("测试类注解和依赖注入验证")
    void testClassAnnotationsAndDI() {
        // 验证依赖注入
        assertThat(ruleService).isNotNull();
        assertThat(authRuleLogMapper).isNotNull();
        assertThat(sequenceIdGen).isNotNull();
        assertThat(txnRuleMatcher).isNotNull();

        // 验证类信息
        assertThat(RuleServiceImpl.class.getSimpleName()).isEqualTo("RuleServiceImpl");
        assertThat(RuleServiceImpl.class.isInterface()).isFalse();
    }

    @Test
    @DisplayName("测试多次调用相同方法")
    void testMultipleCalls() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);

            when(txnRuleMatcher.execute(eq(dataInputDTO), eq(true))).thenReturn(ruleResult);

            // 执行方法多次
            ruleService.executeRule(dataInputDTO);
            ruleService.executeRule(dataInputDTO);

            // 验证调用次数
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("authorization_check_rule", "000"), times(2));
            verify(txnRuleMatcher, times(2)).execute(dataInputDTO, true);
        }
    }

    @Test
    @DisplayName("测试性能 - 大量调用")
    void testPerformance() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);

            when(txnRuleMatcher.execute(eq(dataInputDTO), eq(true))).thenReturn(ruleResult);

            // 测试多次调用的性能
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < 100; i++) {
                ruleService.executeRule(dataInputDTO);
            }
            long endTime = System.currentTimeMillis();

            // 验证性能
            assertThat(endTime - startTime).isLessThan(1000); // 100次调用应该在1秒内完成
            verify(txnRuleMatcher, times(100)).execute(dataInputDTO, true);
        }
    }

    @Test
    @DisplayName("测试边界值 - 空规则类型")
    void testExecuteRule_EmptyRuleType() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            DataInputDTO emptyRuleTypeDTO = new DataInputDTO();
            emptyRuleTypeDTO.setRuleType("");
            emptyRuleTypeDTO.setOrganizationNumber("000");
            emptyRuleTypeDTO.setInput(new HashMap<>());

            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);

            when(txnRuleMatcher.execute(emptyRuleTypeDTO)).thenReturn(ruleResult);

            // 执行方法
            Map<String, String> result = ruleService.executeRule(emptyRuleTypeDTO);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result).hasSize(5);

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("", "000"));
            verify(txnRuleMatcher).execute(emptyRuleTypeDTO);
        }
    }

    @Test
    @DisplayName("测试边界值 - 空组织号")
    void testExecuteRule_EmptyOrganizationNumber() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            DataInputDTO emptyOrgDTO = new DataInputDTO();
            emptyOrgDTO.setRuleType("test_rule");
            emptyOrgDTO.setOrganizationNumber("");
            emptyOrgDTO.setInput(new HashMap<>());

            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);

            when(txnRuleMatcher.execute(emptyOrgDTO)).thenReturn(ruleResult);

            // 执行方法
            Map<String, String> result = ruleService.executeRule(emptyOrgDTO);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result).hasSize(5);

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("test_rule", ""));
            verify(txnRuleMatcher).execute(emptyOrgDTO);
        }
    }

    @Test
    @DisplayName("测试异常处理 - 规则执行返回null")
    void testExecuteRule_RuleExecuteReturnsNull() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);

            when(txnRuleMatcher.execute(eq(dataInputDTO), eq(true))).thenReturn(null);

            // 执行方法
            Map<String, String> result = ruleService.executeRule(dataInputDTO);

            // 验证结果
            assertThat(result).isNull();

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("authorization_check_rule", "000"));
            verify(txnRuleMatcher).execute(dataInputDTO, true);
        }
    }

    @Test
    @DisplayName("测试复杂数据结构处理")
    void testExecuteRule_ComplexDataStructure() {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备复杂数据
            Map<String, Object> complexInput = new HashMap<>();
            complexInput.put("cardNumber", "****************");
            complexInput.put("amount", 1000.50);
            complexInput.put("merchantInfo", Map.of("id", "M001", "name", "Test Merchant"));

            DataInputDTO complexDTO = new DataInputDTO();
            complexDTO.setRuleType("complex_rule");
            complexDTO.setOrganizationNumber("001");
            complexDTO.setInput(complexInput);

            Map<String, Object> complexResult = new HashMap<>();
            complexResult.put("decision", "APPROVE");
            complexResult.put("score", 85.5);
            complexResult.put("reasons", java.util.Arrays.asList("reason1", "reason2"));

            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);

            when(txnRuleMatcher.execute(complexDTO)).thenReturn(complexResult);

            // 执行方法
            Map<String, String> result = ruleService.executeRule(complexDTO);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result.get("decision")).isEqualTo("APPROVE");
            assertThat(result.get("score")).isEqualTo("85.5");
            assertThat(result.get("reasons")).contains("reason1", "reason2");

            // 验证调用
            mockedStatic.verify(() -> RuleMatcherManager.getMatcher("complex_rule", "001"));
            verify(txnRuleMatcher).execute(complexDTO);
        }
    }

    @Test
    @DisplayName("测试线程安全性")
    void testThreadSafety() throws InterruptedException {
        try (MockedStatic<RuleMatcherManager> mockedStatic = mockStatic(RuleMatcherManager.class)) {
            // 准备数据
            mockedStatic.when(() -> RuleMatcherManager.getMatcher(anyString(), anyString()))
                    .thenReturn(txnRuleMatcher);

            when(txnRuleMatcher.execute(any(DataInputDTO.class), eq(true))).thenReturn(ruleResult);

            final boolean[] success = {true};

            Thread thread1 = new Thread(() -> {
                try {
                    for (int i = 0; i < 50; i++) {
                        ruleService.executeRule(dataInputDTO);
                    }
                } catch (Exception e) {
                    success[0] = false;
                }
            });

            Thread thread2 = new Thread(() -> {
                try {
                    for (int i = 0; i < 50; i++) {
                        ruleService.executeRule(dataInputDTO);
                    }
                } catch (Exception e) {
                    success[0] = false;
                }
            });

            // 执行多线程测试
            thread1.start();
            thread2.start();

            thread1.join();
            thread2.join();

            // 验证线程安全
            assertThat(success[0]).isTrue();
        }
    }
}