package com.anytech.anytxn.authorization.service.auth.checkitem;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.dto.ParmAuthCheckControlDTO;
import com.anytech.anytxn.authorization.mapper.merchantblack.MerchantBlacklistSelfMapper;
import com.anytech.anytxn.authorization.base.domain.model.MerchantBlacklist;
import com.anytech.anytxn.authorization.base.enums.StatusEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * @description 商户黑名单检查项测试类
 * <AUTHOR>
 * @date 2025/07/10
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
class MerchantBlankListItemTest {

    @InjectMocks
    private MerchantBlankListItem merchantBlankListItem;

    @Mock
    private MerchantBlacklistSelfMapper merchantBlacklistSelfMapper;

    @Mock
    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;

    @Mock
    private ParmAuthCheckControlDTO parmAuthCheckControlDTO;

    @Mock
    private AuthRecordedDTO authRecordedDTO;

    @Mock
    private CardAuthorizationDTO cardAuthorizationDTO;

    @BeforeEach
    void setUp() {
        when(authorizationCheckProcessingPayload.getAuthRecordedDTO()).thenReturn(authRecordedDTO);
        when(authorizationCheckProcessingPayload.getCardAuthorizationDTO()).thenReturn(cardAuthorizationDTO);
        when(cardAuthorizationDTO.getOrganizationNumber()).thenReturn("001");
        when(authRecordedDTO.getAuthAcquiringIdentificationCode()).thenReturn("MERCHANT001");
        when(authRecordedDTO.getAuthCardAcceptorTerminalCode()).thenReturn("TERMINAL001");
    }

    @Test
    @DisplayName("Check should approve when merchant blacklist info does not exist")
    void testCheck_MerchantBlacklistNotExists() {
        // Arrange
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenReturn(null);

        // Act
        Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should reject when merchant is in blacklist and effective")
    void testCheck_MerchantInBlacklistAndEffective() {
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Arrange
            MerchantBlacklist merchantBlacklist = new MerchantBlacklist();
            merchantBlacklist.setStatus(StatusEnum.EFFECTIVE.getCode());
            merchantBlacklist.setExpireDate(LocalDate.now().plusDays(1));
            when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                    .thenReturn(merchantBlacklist);

            // Act
            Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.REJECT_CODE.getCode(), result);
            verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
        }
    }

    @Test
    @DisplayName("Check should approve when merchant blacklist is effective but expired")
    void testCheck_MerchantBlacklistEffectiveButExpired() {
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Arrange
            MerchantBlacklist merchantBlacklist = new MerchantBlacklist();
            merchantBlacklist.setStatus(StatusEnum.EFFECTIVE.getCode());
            merchantBlacklist.setExpireDate(LocalDate.now().minusDays(1));
            when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                    .thenReturn(merchantBlacklist);

            // Act
            Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
            verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
            verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        }
    }

    @Test
    @DisplayName("Check should approve when merchant blacklist is not effective")
    void testCheck_MerchantBlacklistNotEffective() {
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Arrange
            MerchantBlacklist merchantBlacklist = new MerchantBlacklist();
            merchantBlacklist.setStatus(StatusEnum.INVALID.getCode());
            merchantBlacklist.setExpireDate(LocalDate.now().plusDays(1));
            when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                    .thenReturn(merchantBlacklist);

            // Act
            Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
            verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
            verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        }
    }

    @Test
    @DisplayName("Check should return exception when expire date is null")
    void testCheck_ExpireDateNull() {
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Arrange
            MerchantBlacklist merchantBlacklist = new MerchantBlacklist();
            merchantBlacklist.setStatus(StatusEnum.EFFECTIVE.getCode());
            merchantBlacklist.setExpireDate(null);
            when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                    .thenReturn(merchantBlacklist);

            // Act
            Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode(), result);
        }
    }

    @Test
    @DisplayName("Check should approve when merchant blacklist expires today")
    void testCheck_MerchantBlacklistExpiresOnToday() {
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Arrange
            MerchantBlacklist merchantBlacklist = new MerchantBlacklist();
            merchantBlacklist.setStatus(StatusEnum.EFFECTIVE.getCode());
            merchantBlacklist.setExpireDate(LocalDate.now());
            when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                    .thenReturn(merchantBlacklist);

            // Act
            Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
            verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
            verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        }
    }

    @Test
    @DisplayName("Check should handle unknown status")
    void testCheck_MerchantBlacklistUnknownStatus() {
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Arrange
            MerchantBlacklist merchantBlacklist = new MerchantBlacklist();
            merchantBlacklist.setStatus("UNKNOWN_STATUS");
            merchantBlacklist.setExpireDate(LocalDate.now().plusDays(1));
            when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                    .thenReturn(merchantBlacklist);

            // Act
            Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
            verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
            verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        }
    }

    @Test
    @DisplayName("Check should handle database exception")
    void testCheck_DatabaseException() {
        // Arrange
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);
        });
    }

    @Test
    @DisplayName("AuthMerchantBlackListCheck should delegate to check method")
    void testAuthMerchantBlackListCheck() {
        // Arrange
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenReturn(null);

        // Act
        int result = merchantBlankListItem.authMerchantBlackListCheck(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should handle null organization number")
    void testCheck_NullOrganizationNumber() {
        // Arrange
        when(cardAuthorizationDTO.getOrganizationNumber()).thenReturn(null);
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(isNull(), anyString(), anyString()))
                .thenReturn(null);

        // Act
        Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should handle null acquiring identification code")
    void testCheck_NullAcquiringIdentificationCode() {
        // Arrange
        when(authRecordedDTO.getAuthAcquiringIdentificationCode()).thenReturn(null);
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), isNull(), anyString()))
                .thenReturn(null);

        // Act
        Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should handle null card acceptor terminal code")
    void testCheck_NullCardAcceptorTerminalCode() {
        // Arrange
        when(authRecordedDTO.getAuthCardAcceptorTerminalCode()).thenReturn(null);
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), isNull()))
                .thenReturn(null);

        // Act
        Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should handle empty organization number")
    void testCheck_EmptyOrganizationNumber() {
        // Arrange
        when(cardAuthorizationDTO.getOrganizationNumber()).thenReturn("");
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenReturn(null);

        // Act
        Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should handle empty acquiring identification code")
    void testCheck_EmptyAcquiringIdentificationCode() {
        // Arrange
        when(authRecordedDTO.getAuthAcquiringIdentificationCode()).thenReturn("");
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenReturn(null);

        // Act
        Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("Check should handle empty card acceptor terminal code")
    void testCheck_EmptyCardAcceptorTerminalCode() {
        // Arrange
        when(authRecordedDTO.getAuthCardAcceptorTerminalCode()).thenReturn("");
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenReturn(null);

        // Act
        Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(parmAuthCheckControlDTO).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("测试类注解和依赖注入验证")
    void testClassAnnotationsAndDI() {
        // 验证依赖注入
        assertNotNull(merchantBlankListItem);
        assertNotNull(merchantBlacklistSelfMapper);

        // 验证类信息
        assertEquals("MerchantBlankListItem", MerchantBlankListItem.class.getSimpleName());
        assertFalse(MerchantBlankListItem.class.isInterface());
    }

    @Test
    @DisplayName("测试方法签名验证")
    void testMethodSignatures() throws NoSuchMethodException {
        // 验证主要方法存在
        assertNotNull(MerchantBlankListItem.class.getDeclaredMethod("check",
                AuthorizationCheckProcessingPayload.class, ParmAuthCheckControlDTO.class));
        assertNotNull(MerchantBlankListItem.class.getDeclaredMethod("authMerchantBlackListCheck",
                AuthorizationCheckProcessingPayload.class, ParmAuthCheckControlDTO.class));
    }

    @Test
    @DisplayName("测试多次调用相同方法")
    void testMultipleCalls() {
        // Arrange
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenReturn(null);

        // Act
        merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);
        merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        verify(merchantBlacklistSelfMapper, times(2))
                .selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString());
        verify(parmAuthCheckControlDTO, times(2)).setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
    }

    @Test
    @DisplayName("测试边界值 - 过期日期边界")
    void testCheck_ExpireDateBoundary() {
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Arrange - 测试过期日期为明天
            MerchantBlacklist merchantBlacklist = new MerchantBlacklist();
            merchantBlacklist.setStatus(StatusEnum.EFFECTIVE.getCode());
            merchantBlacklist.setExpireDate(LocalDate.now().plusDays(1));
            when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                    .thenReturn(merchantBlacklist);

            // Act
            Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.REJECT_CODE.getCode(), result);
            verify(authRecordedDTO).setAuthTerminalBlacklistResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
        }
    }

    @Test
    @DisplayName("测试性能 - 大量调用")
    void testCheck_Performance() {
        // Arrange
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenReturn(null);

        // Act - 测试多次调用的性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);
        }
        long endTime = System.currentTimeMillis();

        // Assert - 100次调用应该在合理时间内完成
        assertTrue(endTime - startTime < 1000, "100次方法调用应该在1秒内完成");
        verify(merchantBlacklistSelfMapper, times(100))
                .selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString());
    }

    @Test
    @DisplayName("测试参数传递验证")
    void testCheck_ParameterPassing() {
        // Arrange
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenAnswer(invocation -> {
                    // 验证参数传递正确
                    String org = invocation.getArgument(0);
                    String acquiring = invocation.getArgument(1);
                    String terminal = invocation.getArgument(2);

                    assertEquals("001", org);
                    assertEquals("MERCHANT001", acquiring);
                    assertEquals("TERMINAL001", terminal);
                    return null;
                });

        // Act
        merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        verify(merchantBlacklistSelfMapper).selectByOrgAndBankAndMerchantNumber("001", "MERCHANT001", "TERMINAL001");
    }

    @Test
    @DisplayName("测试返回值类型验证")
    void testCheck_ReturnType() {
        // Arrange
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenReturn(null);

        // Act
        Integer result = merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof Integer);
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
    }

    @Test
    @DisplayName("测试异常处理增强")
    void testCheck_ExceptionHandlingEnhanced() {
        // 测试各种异常类型
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenThrow(new IllegalArgumentException("Invalid argument"))
                .thenThrow(new IllegalStateException("Invalid state"))
                .thenThrow(new UnsupportedOperationException("Unsupported operation"));

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () ->
                merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO));
        assertThrows(IllegalStateException.class, () ->
                merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO));
        assertThrows(UnsupportedOperationException.class, () ->
                merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO));
    }

    @Test
    @DisplayName("测试Mock验证增强")
    void testCheck_MockVerificationEnhanced() {
        // Arrange
        when(merchantBlacklistSelfMapper.selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString()))
                .thenReturn(null);

        // Act
        merchantBlankListItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDTO);

        // Assert - 详细验证Mock交互
        verify(merchantBlacklistSelfMapper, times(1))
                .selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString());
        verify(merchantBlacklistSelfMapper, never())
                .selectByOrgAndBankAndMerchantNumber(isNull(), isNull(), isNull());
        verify(merchantBlacklistSelfMapper, atLeastOnce())
                .selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString());
        verify(merchantBlacklistSelfMapper, atMost(1))
                .selectByOrgAndBankAndMerchantNumber(anyString(), anyString(), anyString());
        verifyNoMoreInteractions(merchantBlacklistSelfMapper);
    }
}